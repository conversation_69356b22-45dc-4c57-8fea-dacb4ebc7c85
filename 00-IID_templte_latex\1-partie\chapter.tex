\chapter{General context of the project}

% \raggedbottom 

 
% Remove minitoc to fix duplicate chapter titles
\minitoc
\section{Introduction}
This chapter begins with an introduction to the organization, Xelops Technology. The issues addressed by the project and its specific objectives are then discussed. The chapter concludes with a description of the work methodology.

\section{Presentation of organization}
\subsection{Presentation of Xelops Technology}

Xelops Technology , known as Neoxia Maroc, is a company that offers consulting and creates digital solutions. It was founded in 2008 in Casablanca, with the support of its former French parent company, Neoxia, which helped it grow in the Moroccan market. \\

Today, Xelops Technology has a strong position in Morocco in all areas: data, development, and DevOps. The company offers innovative and personalized solutions to its clients.\\

Xelops Technology helps all kinds of clients — from big companies to small startups . These areas work together to help clients imagine, create, and run the digital services of the future:

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/Les activites de Neoxia.png}
    \caption{Work areas of Xelops Technology}
    \label{fig:enter-label}
\end{figure}


\subsection{Xelops Technology in Numbers}
\subsubsection{Genenral Numbers}

The figure 1.2  shows some general numbers about Xelops Technology.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/xelps chiffre.png}
    \caption{Some general numbers about Xelops Technology}
    \label{fig:enter-label}
\end{figure}

\subsection{Clients}
Xelops Technology offers services mainly for large companies, both in Morocco and internationally. in The figure 1.3 are some examples of these clients:
\raggedbottom

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/client xelops.png}
    \caption{Clients of Xelops Technology}
    \label{fig:enter-label}
\end{figure}


\subsection{Xelops Technology’s Services}
Xelops Technology helps its clients solve the main challenges of their information systems at different levels:
 
\underline{\textbf{It Strategy and Management Consulting:}}
\begin{itemize} 
    \item Long term planning and it master plans
    \item Enterprise Architecture
    \item System mapping and organization 
\end{itemize}

\underline{\textbf{Project Management:}}
\begin{itemize} 
    \item Project management
    \item Process audit and optimization
    \item Change management
    \item It security
\end{itemize}

\underline{\textbf{It Architecture Consulting:}}
\begin{itemize} 
    \item Information System Architecture:
    \begin{itemize} 
        \item Information management
        \item Software architecture and industrialization platforms
        \item Development of platforms
        \item Software testing and code auditing
    \end{itemize}

    \item It Performance and Security: 
    
    \item Infrastructure:
    \begin{itemize} 
        \item Infrastructure architecture and review
        \item Cloud computing and virtualization 
        \item Optimization of infrastructure investments
    \end{itemize}
\end{itemize}

To provide this support, Xelops Technology carries out different types of missions:

\begin{itemize}
    \item \textbf{Decide:} Strategic planning, Maturity assessment, Organization, Audit, Studies, and Prototyping.
    \item \textbf{Act:} Expertise, Project support, Development and Integration, Support, Coaching and Training, Guidance, management, Business analysis support, Risk management, Quality control.
    \item \textbf{Build knowledge:} Improvement, Industrialization, Methods and best practices.
\end{itemize}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Les differentes typologies de mission menees par Neoxia.png}
    \caption{The different types of missions carried out by Xelops Technology}
    \label{fig:enter-label}
\end{figure}



\section{Presentation of projet}
\subsection{AL MOBADARA Association}

the AL MOBADARA association focuses on charity work,founded in 2005, social integration, and human development. Recognized as a public interest organization and certified ISO 9001-V2015, it has been committed since the beginning to supporting orphans, widows, and other disadvantaged groups . Thanks to an innovative and recognized institutional model, combining originality, creativity, quality, and professional standards, the association has maintained its position and succeeded in its work.

The figure 1.5 shows the different activities of the association:

\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Les differentes activites de lassociation Al Mobadara.png}
    \caption{the different activities of the association AlMobadara}
    \label{fig:enter-label}
\end{figure}


\subsection{Problematic}

AL MOBADARA association has a growing number of beneficiaries for the Kafalat project, which creates increasing challenges. At first, management was done using Excel files and paper folders, that cause  problems like repeated information, lack of tracking, slow information search, and  no reference data or reporting, no overall view of beneficiaries and donors, and difficulty managing the growing number of cases and tracking member activities.

In the figure 1.6 shows some stats about the Kafalat Project:


\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Le projet de parrainage (Kafala) en chiffres.png}
    \caption{Kafalat Project in Numbers}
    \label{fig:enter-label}
\end{figure}

To solve these problems, AL MOBADARA association created a first version of its application to manage its different social projects, including the Kafalat . This application helped to digitalize some tasks, but it has many limits that make managing and following up the association’s activities hard.

Here are the main problems found:
\begin{itemize}
\setlength{\itemindent}{1cm}
    \item \textbf{Fragmented Data Management:} Repeated and inconsistent data.
    \item \textbf{Lack of Advanced Features:} No advanced search, filtering, or good reporting.
    \item \textbf{Unfriendly User Interface:} Not easy to use, causing mistakes and slowing work.
    \item \textbf{No Overall View:} Hard to get a full picture of beneficiaries and donors activities.
   \item\textbf{Difficulty to Grow:} Hard to handle big number of  beneficiaries and donors.
\end{itemize}

\subsection{Project Context and Goals}


AL MOBADARA association is trying to improve its digital platform. The goal is to make it faster and easier to use.

The new version will include:
\begin{itemize}
    \item Better code for improved performance.
    \item A new user interface with additional features as search, export.
    \item New modules like user management, audit trails, and activity tracking.
\end{itemize}

We will have also to mobile apps:
\begin{itemize}
    \item One for donors to view information about their beneficiaries and their services.
    \item One for assistants to help them manage thier beneficiaries.
\end{itemize}

these change is for goal of making the app more useful and efficient and  smoother experience,  the association can continue to grow its charitable work with this system.


\section{Project Management}

The success of a project mostly depends on the development process that guides its different phases. So, choosing a development method that fits the project needs is very important to ensure the final product’s quality.

Project management aims to ensure the smooth progress of a project by organizing all its phases in an evolving and temporary way. For my Final Year Project, I consider four key aspects:

\begin{itemize}
\item \textbf{Functional aspect:} meeting specific needs.
\item \textbf{Technical aspect:} respecting specifications and implementation limits.
\item \textbf{Organizational aspect:} aligning with the target organization’s way of working.
\item \textbf{Time aspect:} respecting deadlines and schedule.
\end{itemize}

In this section, I will talk about  the development process that I followed for my project and explain why I chose it.

\subsection{Agile methodology}

Agile is a method used in software development. It focuses on working closely with the client to build software that meets their real needs. Agile also helps save time by making the project easier to manage.


Agile uses short development cycles. These cycles break the project into small parts and sort them by priority.

Main advantages of Agile:


\begin{itemize}
\item Full involvement and collaboration of the client.
\item It is flexible and adapts to changes.
\item It improves performance and efficiency.
\item It focuses more on working software than long documents.
\end{itemize}

\subsubsection{Scrum}

It’s important to choose a method that fits the project to get a high-quality final product.

In our case, we chose to work with  Scrum. \textbf{Scrum} is an Agile method that helps teams work together step by step to manage the project efficiently. It aims to keep the client happy by quickly responding to their needs.

In our project, \textit{Scrum} defines those main roles:

\begin{itemize}
\item \textbf{The Product Owner:} client representative, responsible for defining product needs and priorities. In our case, \textbf{Mr. ES-SQALLI Tawfik} holds this role.
\item \textbf{The Scrum Master:} responsible for facilitating the application of Agile principles and practices within the team. For our project, this is \textbf{Ms. RAFIK Hanane}.
\item \textbf{The Technical Supervisor:} provides technical support and guides the team in technological choices. In our project, this role is filled by \textbf{Mr. FERHAN Mustapha}.
\item \textbf{The Development Team:} composed of professionals from different fields, responsible for implementing the features. This team is autonomous and makes collective decisions to achieve the project goals.
\end{itemize}

The figure 1.7 contain the Scrum life Cycle:

\medskip

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/processus-1024x421.png}
\caption{Scrum life Cycle}
\label{fig:enter-label}
\end{figure}

\subsection{Project Planning}

During my internship, we used the Scrum method and followed the meetings that this method requires:


\begin{itemize}
    \item \textbf{Sprint Planning Meeting :}This is a very important meeting in Scrum. In this meeting, we choose the most important tasks from the Product Backlog that we want to complete in the sprint.
    \item \textbf{Daily Stand-up :} Every day, we have a short meeting with the Scrum Master to talk about what tasks are done, what we are working on, and what problems we have. This helps the team communicate well and stay updated on progress.
    
    \item \textbf{Sprint Review :} At the end of each sprint, we have a review meeting to check the work done and update the Product Backlog if needed. It’s also a time to plan the next sprint.
\end{itemize}

\subsubsection{Task Management and Tracking}
 
\textbf{→ Trello}\newline
We also used Trello to organize our Scrum tasks. Trello uses visual boards that make it easy to see and manage tasks.\newline

We made boards for our Product Backlog, sprints, and tasks. Each task is a card with details like description, status, and priority.\newline

During sprint planning, we moved cards for selected tasks to the “To Do” list. In daily stand-ups, we updated cards to show progress and issues.\newline

At sprint reviews, we checked cards to see what was done and what to improve. Trello helped us work together clearly and keep track of everything.\newline

the Figure 1.8 shows the Project Tasks by status:

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/Screenshot 2025-06-06 114534}
    \caption{Trello Table}
    \label{fig:enter-label}
\end{figure}


 \subsubsection{Gantt Diagram}

To better understand my work, I created a diagram that shows all the tasks I completed each week during my internship. This helped me manage my time and identify any delays that could affect the deadlines.

Figure 1.9 shows a Gantt chart illustrating the tasks completed each week.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/diagram.png}
    \caption{Gantt Diagram}
    \label{fig:enter-label}
\end{figure}

\section{Conclusion}

This chapter focuses on the presentation of the  organization, the presentation of the project, its problems and its objectives. It also describes the methodology and development process used ,In the next chapter, we will look closely at the Functional Study.
