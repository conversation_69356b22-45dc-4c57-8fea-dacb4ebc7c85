\raggedbottom 

% \addcontentsline{toc}{chapter}{Chapter 3}  

\section{Introduction}

This chapter explains how we designed the new modules and improved existing parts of the project. It lists the completed tasks   and describes the different diagrams used.

 \section{Definition of UML (Unified Modeling Language)}

UML (Unified Modeling Language) is an object modeling language used to represent an information
system. It allows the creation of models that can be applied in any programming language. UML
breaks down the visualization of a model into different diagrams, offering several perspectives of the system. The diagrams we used are:

\begin{itemize}
    \item Use Case Diagram 
    \item Sequence Diagram
    \item Class Diagram
\end{itemize}

\section{Actor Identification}

The first phase of UML formalism is to identify the system actors who interact with it. An actor is an external entity that interacts with the system.

In our context, three types of actors have been identified:

\begin{itemize}
    \item Administrator: Responsible for adding roles, other actors on the platform, and managing them.
    \item Manager: Responsible for creating  assistant profiles and validating their work, and managing donor.
    \item Assistant: Responsible for creating beneficiary, and family profiles, as well as managing donations and support cases. Also responsible for writing periodic reports on the beneficiaries' status.
    \item System: Represents the solution of this project, i.e., a web platform.
\end{itemize}

\raggedbottom

\section{Use Case Diagrams}
\subsection{Global Use Case Diagram}

The diagram, Figure 3.2, groups all the system’s use cases and graphically represents its requirements. It helps identify how users, or actors, interact with the system.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.5\linewidth,height=15cm]{00-IID_templte_latex/images/uml diagram.png}
    \caption{Global Use Case Diagram}
    \label{fig:enter-label}
\end{figure}

The figure above illustrates a global use case diagram showing the interactions between the different actors in the system: Admin, Manager, and Assistant. Each actor has access to a set of specific features according to their role. The Admin is responsible for managing users, roles, audit logs, actions, reports, documents, and tags. The Manager oversees zones, kafalats, donations, donors, EPS centers, complementary support, as well as the validation of reports and beneficiaries. The Assistant manage beneficiaries, families, documents, and reports. All functionalities are connected to the "Authenticate" use case, indicating that user authentication is a prerequisite for accessing the system. This diagram provides a clear overview of the distribution of responsibilities and access rights within the application.

\section{Sequence Diagrams}

Sequence diagrams help show how parts of the system work together over time.\\

Now, we will draw these diagrams to show what happens step by step in each diagram Sequence. This helps us better understand how the system works.

\subsection{Create a User}

The sequence diagram below describes the interactions between the administrator and the system when creating a user.
\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\linewidth,height=10cm]{00-IID_templte_latex/images/diagram seq user creation.png}
    \caption{Sequence Diagram:"Create a User}
    \label{fig:enter-label}
\end{figure}

\subsection{Create a Family}

In this diagram, we show the main steps involved in creating a family.\\
First, we create the mother, then the father, followed by the other family members. Finally, we validate the information of each family member.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\linewidth,height=18cm]{00-IID_templte_latex/images/diagram-export-01-06-2025-18_58_09.png}
    \caption{Sequence Diagram: Create a Family}
    \label{fig:enter-label}
\end{figure}

\subsection{Create a Beneficiary}

The sequence diagram below illustrates the interactions between the assistant and the system during the creation of a beneficiary in the optimized version.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\linewidth,height=18cm]{00-IID_templte_latex/images/creation of beneficiary seq.png}
    \caption{Sequence Diagram: Create a Beneficiary}
    \label{fig:enter-label}
\end{figure}

At the beginning, the assistant must indicate whether the beneficiary is part of a family or independent. If they belong to a family, the assistant must specify the existing family or create a new one if necessary, then select the corresponding family member or create one if it does not yet exist.

Then, the assistant proceeds to enter the following information:

\begin{itemize}
    \item Personal data of the beneficiary,
    \item Health details (allergies, illnesses, disabilities, chronic disease treatments, etc.),
    \item Information about their education level (primary, secondary, university) and any scholarships,
\end{itemize}

At each step, when the assistant moves to the next one by clicking "Next," the entered information is saved. Once the assistant has entered the personal information and proceeds through the remaining steps, the beneficiary is automatically created.
  

\section{Class Diagram}
Class diagrams show the main parts of a system and how they are connected. They help us understand the structure by showing classes, their actions, and how they relate.

Since the project has many classes, we first show a simple diagram without details to give a general idea.

Then, we look at each part (Family, Beneficiary, Donor, Donations, Taken In Charge) in detail, showing their classes, attributes, and actions for a better understanding.


\subsection{Global Class Diagram}
The following class diagram describes the different classes of the system with their associations. The diagram contains 3 types of classes which are:
\begin{itemize}
    \item The yellow tables: these are the main tables that drive the system and are (Beneficiary, Family, Donor, Donation, Taken In Charge)
    \item The light blue tables: these are the tables for documents and notes. The use of these tables aims not to limit the user by predefined fields during entry, which is why we proposed the possibility to add several notes and documents if the user needs them.
    \item The purple tables: these are the remaining tables that the system needs to function properly.
\end{itemize}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.65\linewidth]{images/Diagramme de classes global.png}
    \caption{Global Class Diagram}
    \label{fig:enter-label}
\end{figure}

\subsection{Detailed Class Diagrams}
\subsubsection{Beneficiary Class Diagram}
The diagram below highlights the Beneficiary class, a main class of the system, as well as the classes associated with it. As shown in the diagram, an effort was made to retain as much information as possible about the beneficiary, since this profile is particularly important for the association.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Diagramme de classes beneficiar.png}
    \caption{Beneficiary Class Diagram}
    \label{fig:enter-label}
\end{figure}

\subsubsection{Family Class Diagram}
The class diagram below presents the “Family” class as well as the classes associated with it. The “FamilyMember” class represents a family member and maintains a relationship with the “Person” class, which was already detailed in the section dedicated to beneficiaries.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Diagramme de classe family.png}
    \caption{Family Class Diagram}
    \label{fig:enter-label}
\end{figure}

\subsubsection{Donor Class Diagram}
The class diagram below presents the “Donor” class as well as the classes associated with it. The system distinguishes three types of donors: individual donors and corporate donors , and anonyme donor. Since they share common information, inheritance was used to avoid data redundancy in the representation of donors.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/Diagramme de classes Donateur.png}
    \caption{Donor Class Diagram}
    \label{fig:enter-label}
\end{figure}

\subsubsection{Donations Class Diagram}
The class diagram below presents the “Donations” class as well as the classes associated with it. The system accepts two types of donations: financial donations and natural donations. .

\begin{figure}[H]
    \centering
    \includegraphics[width=0.75\linewidth]{00-IID_templte_latex/images/Diagramme de classes Donation.png}
    \caption{Donations Class Diagram}
    \label{fig:enter-label}
\end{figure}

\subsubsection{Taken In Charge Class Diagram}
The class diagram below presents the “TakenInCharge” class – representing the taken in charge – along with the classes associated with it.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{00-IID_templte_latex/images/Diagramme de classes kafalat.png}
    \caption{Taken In Charge Class Diagram}
    \label{fig:enter-label}
\end{figure}

 
\section{Conclusion}
In conclusion, this chapter detailed the process of designing new modules and optimizing existing parts, clearly defining the tasks to be accomplished and thoroughly explaining the diagrams used.the next chapter will show the Technical part of the project.

