\resume
%\selectlanguage{french}
Lors de mon stage de fin d’études chez Xelops Technology, dans le cadre de mes études à l’École Nationale des Sciences Appliquées de Khouribga, j’ai travaillé sur un projet intéressant. Le but était de créer une application web et mobile pour l’association Almobadara  afin de les aider à gérer les bénéficiaires, les dons , les donateurs et les différents services proposés par l’association.\\

Avec mon équipe, nous avons d’abord étudié les besoins de l’association. Ensuite, nous avons fait un plan clair et rassemblé les informations nécessaires. Nous avons utilisé la méthode Agile Scrum pour construire la plateforme étape par étape. Nous avons aussi utilisé des outils comme Spring Boot, React.js, et Docker. Pour gérer notre travail et les tâches, nous avons utilisé Trello et Azure.\\

Ce projet m’a beaucoup aidé. J’ai utilisé ce que j’ai appris à l’école, j’ai amélioré mes compétences, et j’ai travaillé sur une vraie solution qui aide les autres. À la fin, nous avons créé une plateforme flexible et utile qui répond aux besoins d’Almobadara.


\vspace{1cm}


\noindent\rule[2pt]{\textwidth}{0.5pt}

\textbf{Mots clés :} Association, Application web, Application mobile, Plateforme, Gestion des dons, Bénéficiaires, Agile Scrum, Spring Boot, React.js, Docker.

\\

\\
\noindent\rule[2pt]{\textwidth}{0.5pt}

\clearpage

