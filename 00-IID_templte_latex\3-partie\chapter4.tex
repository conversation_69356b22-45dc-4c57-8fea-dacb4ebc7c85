\raggedbottom 
% \addcontentsline{toc}{chapter}{Chapter 4}
\section{Introduction}
The goal of this chapter is to explain the technologies used in the development, describe the project’s architecture, show how its components work together, and conclude by discussing vibe coding in our programming process.
\section{General Technical Architecture}
The figure presented the global architecture of the management system, highlighting the different components and their interactions, to ensure a smooth and efficient operation of the platform.
\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/arche.png}
\caption{Technical architecture of the project}
\label{fig:enter-label}
\end{figure}
The architecture is organized around several functional layers:
\begin{itemize}
\item Database (PostgreSQL DB): This database stores all essential information needed for the system to function. It also includes Reference Data to ensure consistency and integrity of information.
\item Back End: The core of the application, the back-end is divided into several key components:
\begin{itemize}
\item REST API: This application programming interface allows communication between the front-end and back-end services.
\item Services: Responsible for business logic, these services handle the main operations and business rules of the system.
\item Repositories: They ensure data access and management, facilitating interactions with the database.
\item Master Data: A centralized repository of critical reference information for the application.
\end{itemize}
\item DTO Objects (Data Transfer Objects): These objects are used to transfer data between different layers of the application in a secure and structured way.
\item DAO Objects (Data Access Objects): They allow interaction with the database, performing read, write, update, and delete operations.
\item SSO (Single Sign-On): This single authentication system allows users to log in once to access all parts of the application, improving user experience and security.
\item Web Application: The user interface is developed with ReactJS components, offering an interactive and responsive user experience. Users can easily interact with the system through an intuitive and ergonomic interface.
\item \textbf{Mobile Application:} The mobile application was developed using React Native. It is designed specifically for donors and assistants, standing out for its simplicity and offering a clear and effective user experience (UX) and user interface (UI).

\end{itemize}
\section{Development Framework and Technologies}
\subsection{Client-side Technologies}
\subsubsection{ReactJs}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/ReactJs.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{React}[4] is a free JavaScript library developed by Facebook since 2013. The main purpose of this library is to make it easier to create single-page web applications, by creating components that depend on a state and generate an HTML page with each state change.
\end{minipage}
\end{figure}
\subsubsection{React Native}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.25\textwidth}
\centering
\includegraphics[width=\linewidth]{images/React-native-2-logo.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{React Native}[5] is a framework that allows you to create mobile applications for iOS and Android using JavaScript and React. Instead of writing separate code for each platform, React Native lets you write a single codebase that works on both. It uses native components, which means the application has the look and performance of a real native application.
\end{minipage}
\end{figure}
\subsubsection{Redux}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/redux.jpg}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Redux}[6] is an open-source JavaScript state management library for web applications. It is most commonly used with libraries like React or Angular for building user interfaces.
\end{minipage}
\end{figure}
\subsection{Server-side Technologies}
\subsubsection{Spring Boot}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/springboot.jpg}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Spring Boot}[1] is a framework that makes it easier to develop Spring-based applications by providing tools to get a jar-packaged application that is completely standalone.
\end{minipage}
\end{figure}
\subsubsection{Flyway}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/Flyway.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Flyway}[3] is an open-source tool, under Apache License 2.0, that helps us implement automated and version-based database migrations. It allows us to define required update operations in an SQL script or as Java code.
\end{minipage}
\end{figure}
\subsection{DevOps Technologies}
\subsubsection{Docker}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/Docker.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Docker}[10] is free software that allows you to package an application and its dependencies in an isolated container, which can be run on any server.
\end{minipage}
\end{figure}
\subsubsection{Azure}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/Azure.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Microsoft Azure}[2] is Microsoft's cloud application platform that makes it easier to create, develop, and manage applications through many services (storage, virtual machines, data analysis, workflow, etc.).
\end{minipage}
\end{figure}
\subsection{Persistence Technologies}
\subsubsection{PostgreSQL}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/PostgreSQL.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\url{PostgreSQL}[9] is a relational and object database management system. It is a free tool available under a BSD-type license.
\end{minipage}
\end{figure}
\subsubsection{Redis}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/Redis.png}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{Redis}[8] is an open-source in-memory data structure store (under BSD license) used as a database, cache, message broker, and streaming engine. Redis provides data structures such as strings, hashes, lists, sets, sorted sets with range queries, bitmaps, hyperloglogs, geospatial indexes, and streams.
\end{minipage}
\end{figure}
\subsubsection{MinIO}
\begin{figure}[H]
\centering
\begin{minipage}[b]{0.2\textwidth}
\centering
\includegraphics[width=\linewidth]{images/MinIO.jpg}
\end{minipage}
\hfill
\begin{minipage}[b]{0.7\textwidth}
\textbf{MinIO}[9] is a side software storage stack, it can handle unstructured data such as photos, videos, log files.
\end{minipage}
\end{figure}

\section{Vibe Coding}

In the context of mobile application development, we adopted a \textit{vibe coding} approach. This method emphasizes the use of intelligent tools and AI agents to accelerate and optimize each phase of the development lifecycle — from design and conception to integration and development.

\subsection{Design Phase}

During the design phase, we explored tools that use AI to streamline UX/UI creation. Two tools stood out:

\begin{itemize}
  \item \textbf{UX Pilot:} An AI-powered tool for generating high-quality UI/UX mockups using prompt-based commands. It allows fine-grained control over design components and layouts.
  \item \textbf{Motiff:} The most frequently used tool in our case. Motiff excels in producing professional designs and supports image-based inspiration. However, each screen design is often dependent on the previous one.
\end{itemize}

\begin{figure}[H]
  \centering
  \includegraphics[width=2cm]{00-IID_templte_latex/images/ux pilot.png}
  \hspace{0.5cm}
  \includegraphics[width=2cm]{00-IID_templte_latex/images/motiff.png}
  \caption{UX Pilot and Motiff}
\end{figure}

\subsection{Conception Phase}

In the conception phase, we used two tools for database modeling and system architecture generation:

\begin{itemize}
  \item \textbf{Database Builder:} Generates database schemas and provides exportable SQL scripts.
  \item \textbf{Eraser:} A prompt-based diagramming tool that creates ER diagrams and architecture blueprints.
\end{itemize}

\begin{figure}[H]
  \centering 
  \includegraphics[width=2cm]{00-IID_templte_latex/images/eraser.png}
  \caption{Eraser}
\end{figure}

\subsection{Integration Phase}

Integration was supported by various AI tools for both mobile and web platforms:

\begin{itemize}
  \item \textbf{Bold:} A web-based platform for creating websites using prompts, with exportable code and IDE support.
  \item \textbf{Rork:} A platform for building mobile applications with React Native, requiring no development experience.
\end{itemize}

\begin{figure}[H]
  \centering
  \includegraphics[width=1.5cm]{00-IID_templte_latex/3-partie/bolt.png}
  \hspace{0.5cm}
  \includegraphics[width=2cm]{00-IID_templte_latex/images/rork.png}
  \caption{Bold and Rork}
\end{figure}

\subsection{Development Phase}

For the development phase, we focused on AI coding agents that enable rapid development cycles:

\begin{itemize}
  \item \textbf{GitHub Copilot:} Offers advanced code suggestions and intelligent refactoring.
  \item \textbf{Cursor:} A tool focused on fast prototyping and logical accuracy.
  \item \textbf{Augement:} Known for handling large codebases and structured suggestions. 
\end{itemize}

\begin{figure}[H]
  \centering
  \includegraphics[width=2cm]{00-IID_templte_latex/images/github copilot.png}
  \hspace{0.5cm}
  \includegraphics[width=2cm]{00-IID_templte_latex/3-partie/augement.png}
  \hspace{0.5cm}
  \includegraphics[width=2cm]{00-IID_templte_latex/images/cursor.png} 
  \caption{GitHub Copilot, Augment and Cursor}
\end{figure}



\section{Conclusion}
In summary, this chapter has allowed us to define the key technologies used in the development process. This deep understanding gives us a solid foundation for making informed decisions and fully harnessing the potential of technological innovations in our future projects.the next chapter will show the final Result.