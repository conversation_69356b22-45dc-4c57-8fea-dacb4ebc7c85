\introduction


In a world where digitalization is constantly evolving, almost everything today can be done through websites and digital platforms. The use of paper is becoming a thing of the past. With the rise of online tools, data security, and export features, working through web platforms is more efficient, practical, and secure.\\

In our case, Al Mobadara Association manages many of its activities using Excel sheets. This method is useful, but it is no longer adapted to their growing needs. So the time came to move into a more organized, secure, and easy way so the association can do their work.\\

To solve this problem, we developed both web and mobile applications using React, Spring Boot, and React Native. We focused on applying best development practices to make their daily tasks simpler and more organized .Key features include advanced search and export functionalities, a dashboard displaying main statistics, and a clean, optimized codebase. Additionally, we created a mobile app tailored for assistants and donors.\\

This report includes five chapters :
\begin{enumerate} 
  \item The chapter 1 presents the general context: the host organization, project challenges, objectives, and work methodology.
  \item The chapter 2 studies the functional part of the project by describing the main modules of the project.
  \item The chapter 3 covers the conception with UML diagrams of the importants modules.
  \item The chapter 4 chapter discusses the technical part, including the architecture used to implement the project.
  \item The chapter 5  shows the current results of the application web and mobile.
\end{enumerate}
