\raggedbottom 

% \addcontentsline{toc}{chapter}{Chapter 5}  

\section{Introduction}
In this chapter, we will begin by presenting the different screens of the platform, both web and mobile. We will finish by describing the migration process.

\section{Web graphic interfaces}
\subsection{Authentication Page}

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/loginPage.png}
\caption{Login page}
\label{fig:enter-label}
\end{figure}

This screen shows how users can log in using Microsoft. Users must be added by the admin before they can access the app.

\subsection{Dashboard}
\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/dashbaord.png}
\caption{Dashboard}
\label{fig:enter-label}
\end{figure}

Here we can see a dashboard with different parts of the app (general dashboard, donors, kafalats, donations, beneficiaries, etc.). It uses clear graphics to show the main statistics of the app.
\subsection{Donor}
In the donors section, we can see a list of all donors. We can filter them using an advanced filter, export the list as a CSV file, add new donors, or view details of a donor with all the related sub-modules.

This figure shows the list of donors and the different actions we can perform:
\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/list of donateurs.png}
\caption{Donors list}
\label{fig:enter-label}
\end{figure}

And in this screen shows how we can add a physical donor with all the important information:

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/add donor physque.png}
\caption{Add Physical Donor}
\label{fig:enter-label}
\end{figure}

\subsection{Beneficiary}
In the Beneficiary section, assistants can add, view, and manage beneficiaries. A beneficiary is first created as a pre-candidate. Then, they must be validated by the assistant and later by other roles to become a candidate. When a candidate is linked to a donor, they officially become a beneficiary.

In this screen, we can see the list of beneficiaries with all available actions such as filter, add, delete, update, or export as a CSV file.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/beneficiary list.png}
\caption{Beneficiaries list}
\label{fig:enter-label}
\end{figure}

Here we can see the beneficiary profile with all the sub-modules like notes, actions, documents, and more.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/beneficiary fiche.png}
\caption{Beneficiary profile}
\label{fig:enter-label}
\end{figure}

\subsection{Family}
The Family section is used to create families, where each member can later become a beneficiary. A family should include a father, a mother, and other family members. We can add, view, update, and see the family profile.

In this screen, we can see the list of families with all the main actions.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/family list.png}
\caption{List of family profiles}
\label{fig:enter-label}
\end{figure}

Here we can see the profile of a family, where we find the family members and the other sub-modules.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/fiche of family.png}
\caption{Family profile}
\label{fig:enter-label}
\end{figure}

\subsection{Kafalat}
The Kafalat section is where we link donors with candidates for a specific service (for example, the widow support service). 

In this section, we can manage the donations from a donor to a beneficiary by creating operations—either one-time or monthly—each with a specific amount.

In this screen, we see the list of Kafalats with the main actions.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/kafalat list.png}
\caption{Kafalat List}
\label{fig:enter-label}
\end{figure}

Here we see the profile of a Kafalat and how we manage its operations.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/fiche of kafalat.png}
\caption{Kafalat profile}
\label{fig:enter-label}
\end{figure}
\subsection{Aide Complémentaire}

The Aide Complémentaire section is used to create campaigns that can include one or more beneficiaries, or groups of beneficiaries. Donors can give donations to these campaigns. A campaign can be a project or a type of support, like Eid Adha or Ramadan Baskets.

In this screen, we can see the list of campaigns with all the main actions.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/aide complementaire lsit.png}
\caption{Aide Complémentaire list}
\label{fig:enter-label}
\end{figure}

In this screen, we can see the profile of a campaign. Here, we can collect donations from donors and choose the beneficiaries who will receive the support.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/aide fiche.png}
\caption{Aide Complémentaire profile}
\label{fig:enter-label}
\end{figure}

\subsection{Administration}

In the administration section, we can manage users, audit logs, assistants, tags, documents, and many other features. In the next sections, we will show some of the things that can be managed.

\subsubsection{Assistants}

In this part, we can manage assistants — add new ones or update their information, as shown in the screen below.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/assistants list.png}
\caption{Assistants list}
\label{fig:enter-label}
\end{figure}

\subsubsection{Tagging}

Here, we can manage the tags used in the app. We can add new tags as needed.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/tags list.png}
\caption{Tags list}
\label{fig:enter-label}
\end{figure}

\subsubsection{Audit}

In this section, we can view the audit log. It shows what each user has done, including detailed information about every action.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/audit list.png}
\caption{Audit log}
\label{fig:enter-label}
\end{figure}

\subsubsection{Documents}

Here, we can manage documents that are about to expire or are already expired.

\begin{figure}[H]
\centering
\includegraphics[width=1\linewidth]{00-IID_templte_latex/images/documents.png}
\caption{Document management}
\label{fig:enter-label}
\end{figure}

\section{Mobile App}
\subsection{Donor Application}
The Donor Application provides donors with access to all essential information. It allows them to view their beneficiaries, track their donations (Kafalat), access important documents, and interact with key features designed to enhance transparency and communication.

\subsubsection{Login and Home Page}
The application opens with a login screen where the donor must enter a valid email and password to access their account. Once logged in, the Home Page displays a summary of the donor’s latest beneficiaries and recent reports.
\begin{figure}[H]
  
  \subfigure[Login Page]{
    \includegraphics[width=0.35\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (1).png}
  }
  \hfill
  \subfigure[Home Page]{
    \includegraphics[width=0.35\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (2).png}
  }
  \caption{Login and Home Page}
  \label{fig:donor-app}
\end{figure}

\subsubsection{Kafalat and Reports}
In this section, the donor can view their Kafalat operations and related beneficiaries. Reports are available for download in three languages: English, French, and Arabic. Each Kafalat entry contains detailed information about the donation and the associated beneficiary.
\begin{figure}[H]
  
  \subfigure[Kafalat Page]{
    \includegraphics[width=0.30\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (3).png}
  }
  \subfigure[Operations Page]{
    \includegraphics[width=0.30\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (4).png}
  }
  \subfigure[Reports Page]{
    \includegraphics[width=0.30\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (6).png}
  }
  \caption{Kafalat and Reports Page}
  \label{fig:donor-app}
\end{figure}
\subsubsection{Profile and Donations}
The Profile Page displays the donor’s personal and contact information. For corporate (moral) donors, additional organizational details may be shown. A separate screen provides a list of donations made by the donor, including dates, incoming amounts, and expenditures.
\begin{figure}[H]
  
  \subfigure[Profile Page]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (7).png}
  }
  \hfill
  \subfigure[Donations Page]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (5).png}
  } 
  \caption{Profile and Donations Page}
  \label{fig:donor-app}
\end{figure}
\subsubsection{Reclamation}
This section includes a Reclamation Page where donors can submit complaints or feedback. Once a reclamation is sent, the user can expect a response from the application’s administrative team.
\begin{figure}[H]
  
  \subfigure[Reclamation Page]{
    \includegraphics[width=0.30\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (8).png}
  }
   \hfill
  \subfigure[Add Reclamation Page]{
    \includegraphics[width=0.30\textwidth, height=8cm]{00-IID_templte_latex/images/Donateur (9).png}
  } 
  \caption{Reclamation Section }
  \label{fig:donor-app}
\end{figure}
\subsection{Assistant Application}
The Assistant application provides access to detailed information about beneficiaries and families within their assigned zone, similar to the Donor interface in the UI/UX. 

However, the key added feature for assistants is the interactive map. This map allows assistants to locate their beneficiaries efficiently, plan optimal routes, and even add comments for each visit. Additionally, assistants can scan the beneficiaries' locations and upload documents directly from the app.
\begin{figure}[H]
  \subfigure[Home Page]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Assistant (1).png}
  } 
  \hfill 
  \subfigure[Map Page]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Assistant (2).png}
  }
  \caption{Assistant Pages }
  \label{fig:donor-app}
\end{figure}

\begin{figure}[H]
  \subfigure[Chosing of Benenficaires]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Assistant (3).png}
  }
  \hfill
  \subfigure[Route Page]{
    \includegraphics[width=0.25\textwidth, height=8cm]{00-IID_templte_latex/images/Assistant (4).png}
  }
  \caption{Assistant Pages }
  \label{fig:donor-app}
\end{figure}
\section{Architecture of the Migration}

The migration process was designed to be simple, clear, and efficient. It followed these main steps:

\begin{itemize}
\item \textbf{Data Collection:} We gathered all the Excel files containing the existing data of Al Mobadara.

\item \textbf{Data Preparation:} Python scripts were used to clean the data, remove duplicates, and convert it into a format compatible with our application's logic.

\item \textbf{Migration Service:} A special service was created in the back-end called \texttt{migration}. It contains APIs that accept Excel files and use service functions to insert the cleaned data into the database.

\item \textbf{Pre-production Testing:} All migration steps were tested in the pre-production environment to ensure that the data was correctly stored and that the application worked as expected.
\end{itemize}

This approach helped us move from Excel-based data to a structured and functional database in our new platform.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{image.png}
    \caption{Architucture of the Migration}
    \label{fig:enter-label}
\end{figure}


 \section{Conclusion}
In this chapter, we end the report by presenting the realization part, starting with the web screens, moving to the mobile interfaces, and ending by discussing the migration part.