\raggedbottom 

% \addcontentsline{toc}{chapter}{Chapter 2}  
\section{Introduction}
This chapter introduces the functional requirements of the application to be implemented. defining the terminology and system concepts, then examine the existing system and specify the
requirements. Finally,  present the Proposed solution and the modules that our application will
contain.
\section{Terminology and Concepts}
To ensure that the association has a good user experience with the system, we have carefully chosen the terms to be used in this system as shown in table 2.1:


\begin{longtable}{|p{4cm}|p{10cm}|}
\hline
\textbf{Term} & \textbf{Description} \\
\hline
\textbf{Donor} & Any person who makes or wishes to make a donation. A donor makes one or more donations to the association. \\
\hline
\textbf{Beneficiary} & Any person receiving a service funded by one or more donors. \\
\hline
\textbf{Service} & The set of services offered by the association (Orphan <PERSON>, Ramadan Campaign 2021, Surgical Operation, etc.) \\
\hline
\textbf{Complementary help} & These are campaigns where donations are intended for this help, in cases such as Ramadan baskets, <PERSON>id al<PERSON>, etc. \\
\hline
\textbf{EPS} & EPS are the association's centers, where internal beneficiaries are located in each center. \\
\hline
\textbf{Kafalat} & The relationship between one or more donors, a service, and possibly one or more beneficiaries. \\
\hline
\textbf{User} & Any individual using the system, whether an administrator or association member. \\
\hline
\textbf{Families} & The families of beneficiaries who may be involved or affected by kafalat and services provided by the association. \\
\hline
\textbf{Donations} & Financial or natural contributions made by donors to support the association's various programs and services. \\
\hline
\caption{System Terminology}
\end{longtable}


\section{Specification of Requirements}
\subsection{Study of the Existing System}
Before the implementation of the web management platform, the Al Mobadara association primarily used Excel files and paper folders to manage its activities. This traditional method had limitations in terms of efficiency and data traceability. For example, the kafalat application form illustrates the previous management mode.

Figure 2.1 shows how the Kafalat Application form was:

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{images/Formulaire de candidature pour parrainage.png}
    \caption{Kafalat Application Form}
    \label{fig:enter-label}
\end{figure}

The current web platform helps the association manage its work online. It is used to handle donor and beneficiary profiles, donations, and support cases.\\

Even though it has some useful features, the platform still has problems. It needs better code to run faster and more smoothly. A good search and filter system is also important to quickly find the right information.\\

To fix these problems, a data repository should be added. This will store all the association's data in one place. It will keep the data organized and consistent. It will also make updates easier. With this system, the platform will be faster, easier to use, and better at meeting the association’s growing needs.\\


\subsection{Functional Requirements}
To improve the platform, we plan to:

\begin{itemize}
\item \textbf{Fix and improve the code}: We will review the code to make the platform faster and more stable.
\item \textbf{Redesign the user interface}: We will create a new design that is easier and nicer to use. 
\item \textbf{Add new modules}: We will add parts for managing users and tracking what they do, and other parts like EPS, and Tagging System. 
\item \textbf{Add search and filter}: We will add ways to quickly find the information users need. 
\item \textbf{Make special mobile areas}: We will make mobile apps for donors and assistants. Donors can see their donations and reports. assistants can manage beneficiaries and their related information.
\end{itemize}

With all these changes, the platform will work better and serve the association’s needs more effectively.


\subsection{Non Functional Requirements}
Non functional requirements are rules that help improve our system work to work well. They help make the software better and more reliable. Here are the main non functional needs for our application:

The application must be secure by managing who can access what.

\begin{itemize}
    \item The code should be simple and clear, so it’s easy to update or improve later.
    \item The app should be easy to use, with a clean and friendly interface.
    \item The app should work well in any situation, no matter where or how it’s used.
    \item The app should be secured , with role management and secured endpoints.
\end{itemize}


\subsection{Proposed Solution}

After studying the current system and the needs of the Al Mobadara association, it became clear that moving to a web and mobile platform is very important. Digital tools help collect all data, make tasks easier, and help follow what is happening. They also help the association work better, be more open, and have a bigger positive impact.
\vspace{0.1cm}

New features will also be added. These will help manage users better and follow activities more closely. Users will also be able to get better reports and have more control over roles and permissions.\\

We also want to add a strong search and filter system so users can quickly find the information they need. This will help them save time and be happier using the app.\\

Another idea is to create a special mobile area for donors and assistants. There, they can check their personal info, donation history, reports, and the people they help—anytime, from anywhere.\\
 

\begin{figure}[H]
    \centering
    \includegraphics[width=0.65\linewidth]{00-IID_templte_latex/images/_- visual selection (1).png}
    \caption{Proposed Solution}
    \label{fig:enter-label}
\end{figure}

\section{Module Presentation}
\subsection{Authentication Module}
To ensure REST-API security, it is imperative to implement robust measures to prevent any unauthorized access attempts to services. Thus, each unprivileged access attempt will be automatically blocked. This means no one can access the platform without prior authentication. By establishing these protections, we ensure data confidentiality and overall system security, thereby strengthening user trust and preserving service integrity.

\subsection{Administration Module}
The brand-new administration module will serve as a central hub for administrators, offering a user-friendly and intuitive interface to oversee all aspects of the platform. It will encompass three main sub-modules:

\begin{itemize}
    \item User Management: This section will allow administrators to manage user accounts, define access rights, and assign specific roles to team members. Advanced features include creating personalized user profiles, managing user sessions, and resetting passwords.
    \item Profile Management: This sub-module will offer detailed management of user profiles, enabling administrators to customize settings based on individual user needs. Advanced features include managing access permissions, customizing profile settings, and viewing platform usage reports.
    \item Audit Management: This section will provide tracking and monitoring tools to control platform activity, ensuring regulatory compliance and data security. Advanced features include generating personalized audit reports, real-time monitoring of system events, and managing activity logs.
    \item User Actions Management: This sub-module will allow tracking the status and progress of actions undertaken by users, as well as their modification and deletion.
    \item Tag Management: This section allows creating tags with different types to facilitate filtering in the main modules. The user can associate tags with specific modules. For example, a donor can be identified by a "VIP" tag.
    \item Documents to Renew: This sub-module allows tracking the status of documents, highlighting those that are expired or nearing expiration.
    \item Services: This section allows defining the services available in the application, organized by categories. For example, the Kafalat category may contain the Orphan Kafalat service.
\end{itemize}

By merging these advanced features into the new administration module, the improved solution will provide the Al Mobadara association with complete control over its platform, ensuring efficient and secure management of its operations. This module will be a crucial pillar of the association's growth and development strategy, enabling it to proactively and professionally meet the growing needs of its community.

\subsection{Management Modules}
\subsubsection{Beneficiaries}
At this stage, the application must allow the administrator to create, modify, and delete one or more beneficiaries. This module will also include the management of different educational levels, EPS centers, assistants, health status, and the family linked to a given beneficiary. Thus, adding a beneficiary requires:
\begin{itemize}
    \item Selecting the family and the concerned family member.
    \item Selecting the service they will benefit from.
    \item Providing personal information.
    \item Providing the EPS center in case a beneficiary resides in one of the association's centers.
    \item Providing health status information.
    \item Providing the beneficiary's educational history (institution, level, city, branch, grade...).
\end{itemize}

\subsubsection{Donors}
The goal is to allow the creation, modification, or deletion of a donor.

This module will also include the management of donations and support cases associated with a given donor. There are two types of donors: individual and corporate.

To add an individual donor, the following steps are required:
\begin{itemize}
    \item Provide their personal information.
\end{itemize}

To add a corporate donor, the following steps are required:
\begin{itemize}
    \item Provide company information.
    \item Provide personal information of one or more contacts who will represent the corporate donor.
\end{itemize}

The application also allows managing anonymous donors, initially identified by an abstract name like Anonymous 1. These donors can later be reclassified as individual or corporate donors once their identity is confirmed.

\subsubsection{Families}
The goal is to facilitate the creation, modification, or deletion of a family.

This module also includes the management of different family members, income, banking information, and support cases benefiting the family. Thus, adding a family requires:
\begin{itemize}
    \item Providing information about the mother.
    \item Providing information about the father.
    \item Providing information about other family members.
    \item Selecting the guardian of this family.
\end{itemize}

\subsubsection{Donation}
In this section, the goal is to allow the creation, modification, or deletion of a donation.

This module includes two types of donations: financial and in-kind. To add a financial donation, the following steps are required:
\begin{itemize}
    \item Select the donor.
    \item Indicate the amount.
    \item Choose the services (Kafalat, Complementary Aid...).
    \item Choose the payment channel.
\end{itemize}

To add an in-kind donation, the following steps are required:
\begin{itemize}
    \item Select the donor.
    \item Add the list of products.
    \item Choose the services (Kafalat, Complementary Aid...).
    \item Indicate the estimated value of the donation.
\end{itemize}

\subsubsection{Support Cases}
The goal of this module is to facilitate the creation, planning, modification, and deletion of a support case.

This module also includes the planning and execution of the support case, as well as account management (Donor, Beneficiary) to debit the concerned donor's account and credit the beneficiary's account. Thus, adding a support case involves the following steps:
\begin{itemize}
    \item Selecting one or more donors.
    \item Selecting one or more beneficiaries.
\end{itemize}
Once the support case is created, it must be planned by:
\begin{itemize}
    \item Specifying the type of support case (simple, recurring).
    \item Indicating the amount.
    \item Specifying the periodicity (for a recurring support case).
    \item Indicating management fees.
    \item Specifying the planning date.
    \item Debiting the total amount from the donor's account.
\end{itemize}

Once the planning date is reached, the beneficiary's account will be credited, and the support case will be executed as soon as the beneficiary withdraws their money.

\subsubsection{EPS}

The goal of this module is to facilitate the creation, planning, modification, deletion, and management of internal centers. Each center can contain:

\begin{itemize}
    \item A list of managers affiliated with the center.
    \item A list of internal beneficiaries.
    \item One or more collection services dedicated to this center.
\end{itemize}

The main steps are as follows:

\begin{itemize}
    \item Creation of an EPS center with the following information: name, capacity, type of beneficiaries, address, etc.
    \item Linking the EPS center to a specific zone.
    \item Creation of a service associated with this center.
    \item Planning a collection date for this service, with the possibility of specifying the month and year of the collection.
\end{itemize}

\textbf{→ Management Rules}
Additionally, management rules are established for each module, such as beneficiary types, donor types, beneficiary status steps, etc., to ensure consistent and efficient system operation.

\begin{figure}[H]
    \centering
    \includegraphics[width=1\linewidth]{table.png}
    \caption{Management Rules }
    \label{fig:enter-label}
\end{figure}

\section{Conclusion}

‎ In summary, this chapter has established the functional requirements of the application to be developed. We have defined the terminology and system concepts, specified the requirements based on the existing system, and presented the chosen solution with corresponding modules. These steps lay the essential foundation for the future development of the application. The next chapter contain Conception of the Project.



