%--------------A VOTRE ATTENTION-------------%
% Les étudiants  qui disposent de plus de 3 chapitres dans leurs travaux peuvent en complèter
% Les Membres doivent figurer dans la dernière version finale du mémoire pour dépôt de mémoire

\documentclass[hidelinks]{iid}
\usepackage{titletoc}
\usepackage{subfigure}
\usepackage{arabtex}
\usepackage{utf8}
\usepackage{hyperref}  % This enables clickable links
 
% \usepackage{polyglossia}
% \setmainlanguage{english}
% \setotherlanguages{arabic} 

\setlength{\glsdescwidth}{0.65\textwidth}
% \usepackage{lscape}
\usepackage[acronym]{glossaries} 
\makeglossaries
\typeMemoire{Engineering Degree Diploma}
\optionFormation{\textbf{Computer Science and Data Engineering}\\ \includegraphics[scale=0.08]{images/logoIID.png}}
\etudiant{NACHID \textbf{<PERSON><PERSON>}}
\titreDuMemoire{Implementation of a Web and Mobile application to optimize association management} 
\dateSoutenance{ }
%\promo{2\up{ème}}
\anneeScolaire{\the\year}



%%maitre de mémoire
\encadrants{Ms.KAROUM  \textbf{Bouchra}, ENSA khouribga\\ 	
Mr.ES-SQALLI  \textbf{Tawfik}, Xelops Technology}

%% Membres du Jury


% \hypersetup{
% pdftitle={--},
% pdfauthor={--},
% pdfsubject={--},
% pdfkeywords={--} 
% }

% \color{bookColor}

% %importation du glossaire
% \loadglsentries{glossaire_reduit}

\begin{document}




\pageDeGarde
% %\pageTitre

\pagecolor{white}

% %% page vide
\thispagestyle{empty}\ \clearpage

% \newpage
% % sommaire
% \pagenumbering{roman}

\setcounter{tocdepth}{0}
% \startlist{toc}
% \printlist{toc}{}{\chapter*{Sommaire}}
\setcounter{tocdepth}{5}

%% rdedicaces
\include{0-dedicace}
\newpage 

%% remerciements
\include{1-remerciements}
\newpage 

% Résume
\include{2-resume}
\newpage

% Résume
\include{2-2-resumeAn}
\newpage

% Résume
\include{2-3-resumeAr}
\newpage

\selectlanguage{english}
 
\tableofcontents
\newpage

% %liste des figures
\listoffigures 
\newpage

%liste des tableaux
\listoftables
\newpage

%%liste des algo
% \listofalgorithmes
%\newpage
%


%% Les sigles et acronymes
% \include{2-4-listedessigles}
\newpage

%% Les sigles et acronymes
% \setglossarystyle{altlist}
% \printglossary[title=Liste des acronymes, toctitle=Liste des acronymes, type=\acronymtype]
%\newpage

% Le glossaire proprement dit
%\setglossarystyle{super}
%\printglossary[type=main]


\pagenumbering{arabic}
% \setcounter{page}{1}
%%introduction
\include{3-introduction}
\lhead[]{} \rhead[]{} \chead[]{}
\selectlanguage{english}
\fancyhead[L]{\tiny \leftmark}
\fancyhead[R]{\scriptsize \rightmark}
\fancyfoot[C]{\thepage}

% \chapter{Functional Study}\label{chap:2} 
 \input{00-IID_templte_latex/1-partie/chapter}

\chapter{Functional Study}\label{chap:2} 
 \input{00-IID_templte_latex/2-partie/chapter2}
\chapter{Conception}\label{chap:3} 
 \input{3-partie/chapter3}
 \chapter{Technical Study}\label{chap:4} 
 \input{3-partie/chapter4} 
 \chapter{Realization}\label{chap:5} 
 \input{3-partie/chapter5} 
  
 
\include{perspectives}
% %%conclusion
\include{4-conclusion}
% 
\lhead[]{} \rhead[]{} \chead[]{}
\newpage
\addcontentsline{toc}{chapter}{Webliography}
\chapter*{Webography}
 
\bibitem{lambert2020}
 

\bibitem{springboot}
[1] \textit{Discover the Spring Boot Framework}. (n.d.). OpenClassrooms. \\
\url{https://openclassrooms.com/fr/courses/4668056-construisez-des-microservices/7651525-decouvrez-le-framework-spring-boot}

\bibitem{azure}
[2] \textit{What is Azure – Microsoft Cloud Computing Services}. (n.d.). Microsoft Azure. \\
\url{https://azure.microsoft.com/fr-fr/overview/what-is-azure/}

\bibitem{flyway}
[3] AXOPEN. (2021, March 11). \textit{Flyway: The tool for database migration}. \\
\url{https://www.axopen.com/migration-de-donnees-lyon/}

\bibitem{reactjs}
[4] \textit{ReactJS Documentation}. \\
\url{https://reactjs.org/}

\bibitem{reactnative-old}
[5] \textit{React Native Documentation}. \\
\url{https://reactjs.org/}

\bibitem{redux}
[6] \textit{Redux Documentation}. \\
\url{https://redux.js.org/}

\bibitem{minio}
[7] \textit{MinIO Documentation}. \\
\url{https://min.io/}

\bibitem{redis}
[8] \textit{Redis Documentation}. \\
\url{https://redis.io/}

\bibitem{postgresql}
[9] \textit{PostgreSQL Documentation}. \\
\url{https://www.postgresql.org/docs/}

\bibitem{docker}
[10] \textit{Docker Documentation}. \\
\url{https://docs.docker.com/guides/}


%\include{annexe}



\end{document}          
